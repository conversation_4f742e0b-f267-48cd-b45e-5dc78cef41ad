/**
 * 配送费相关API
 */

import { http } from '@/utils/http'
import { calculateDistance, calculateDistanceBasedDeliveryFee } from '@/utils/distance'

/**
 * 配送费配置接口
 */
export interface IDeliveryConfig {
  deliveryBaseFee: number // 基础配送费
  deliveryFreeEnabled: boolean // 是否启用免配送费
  deliveryFreeAmount: number // 免配送费门槛金额
  deliveryDiscountEnabled: boolean // 是否启用配送费折扣
  deliveryDiscountAmount: number // 配送费折扣门槛金额
  deliveryDiscountRate: number // 配送费折扣率
  deliveryKmFee?: number // 基于距离的配送费（预留）
  deliveryMinOrderAmount?: number // 最低起送金额（预留）
}

/**
 * 系统配置响应接口
 */
export interface ISystemConfigResponse {
  id: number
  configKey: string
  configValue: string
  configType: string
  category: string
  description: string
  version: number
  status: number
  isSystem: number
  createdAt: string
  updatedAt: string
}

/**
 * 分页响应接口
 */
export interface IPaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

/**
 * 配送费计算请求参数
 */
export interface IDeliveryFeeCalculateRequest {
  merchantId: number // 商家ID
  totalAmount: number // 订单金额
  distance?: number // 配送距离（可选）
}

/**
 * 配送费计算响应
 */
export interface IDeliveryFeeCalculateResponse {
  deliveryFee: number // 配送费
  originalFee: number // 原始配送费
  discountAmount: number // 折扣金额
  isFree: boolean // 是否免费
  freeThreshold?: number // 免费门槛
  discountThreshold?: number // 折扣门槛
  discountRate?: number // 折扣率
  distance?: number // 配送距离（公里）
}

/**
 * 获取配送费配置
 * @returns 配送费配置信息
 */
export const getDeliveryConfig = async (): Promise<IDeliveryConfig> => {
  try {
    const response = await http.get<IPaginationResponse<ISystemConfigResponse>>(
      '/api/v1/user/system/configs/details',
      {
        page: 1,
        page_size: 10,
        query: '',
        category: 'deliveryFee',
      },
    )
    console.log('获取配送费配置响应:', response.data)

    if (response.data && response.data.list && response.data.list.length > 0) {
      const configItem = response.data.list[0]
      console.log('找到配送费配置项:', {
        configKey: configItem.configKey,
        configType: configItem.configType,
        configValue: configItem.configValue,
      })

      if (configItem.configType === 'json') {
        try {
          const config = JSON.parse(configItem.configValue) as IDeliveryConfig
          console.log('解析后的配送费配置:', config)
          return config
        } catch (error) {
          console.error('解析配送费配置失败:', error)
          console.error('原始配置值:', configItem.configValue)
          return getDefaultDeliveryConfig()
        }
      } else {
        console.warn('配送费配置类型不是json:', configItem.configType)
      }
    } else {
      console.warn('未找到配送费配置数据:', {
        hasData: !!response.data,
        hasList: !!(response.data && response.data.list),
        listLength: response.data?.list?.length || 0,
      })
    }

    // 如果没有找到配置，返回默认配置
    return getDefaultDeliveryConfig()
  } catch (error) {
    console.error('获取配送费配置失败:', error)
    return getDefaultDeliveryConfig()
  }
}

/**
 * 获取默认配送费配置
 * @returns 默认配送费配置
 */
export const getDefaultDeliveryConfig = (): IDeliveryConfig => {
  return {
    deliveryBaseFee: 10.0,
    deliveryFreeEnabled: false,
    deliveryFreeAmount: 0,
    deliveryDiscountEnabled: false,
    deliveryDiscountAmount: 0,
    deliveryDiscountRate: 1.0,
    deliveryKmFee: 0,
    deliveryMinOrderAmount: 0,
  }
}

/**
 * 计算配送费
 * @param config 配送费配置
 * @param totalAmount 订单金额
 * @param merchantId 商家ID
 * @param distance 配送距离（可选）
 * @param userLat 用户纬度（可选）
 * @param userLng 用户经度（可选）
 * @param merchantLat 商家纬度（可选）
 * @param merchantLng 商家经度（可选）
 * @returns 配送费计算结果
 */
export const calculateDeliveryFee = (
  config: IDeliveryConfig,
  totalAmount: number,
  merchantId?: number,
  distance?: number,
  userLat?: number,
  userLng?: number,
  merchantLat?: number,
  merchantLng?: number,
): IDeliveryFeeCalculateResponse => {
  let originalFee = config.deliveryBaseFee
  let deliveryFee = originalFee
  let discountAmount = 0
  let isFree = false
  let calculatedDistance = distance

  // 如果提供了坐标信息，计算距离
  if (!calculatedDistance && userLat && userLng && merchantLat && merchantLng) {
    calculatedDistance = calculateDistance(userLat, userLng, merchantLat, merchantLng)
    console.log(`🚚 计算配送距离: ${calculatedDistance}km`)
  }

  // 如果启用了基于距离的配送费计算
  if (config.deliveryKmFee && config.deliveryKmFee > 0 && calculatedDistance) {
    originalFee = calculateDistanceBasedDeliveryFee(
      calculatedDistance,
      config.deliveryBaseFee,
      3, // 免费配送距离3km
      config.deliveryKmFee,
    )
    deliveryFee = originalFee
    console.log(`🚚 基于距离的配送费: ${originalFee}元 (距离: ${calculatedDistance}km)`)
  }

  // 检查满额免配送费
  if (config.deliveryFreeEnabled && totalAmount >= config.deliveryFreeAmount) {
    deliveryFee = 0
    discountAmount = originalFee
    isFree = true
    console.log(
      `🚚 满额免配送费: 订单金额${totalAmount}元 >= 免费门槛${config.deliveryFreeAmount}元`,
    )
  }
  // 检查满额配送费折扣（只有在不免费的情况下才计算折扣）
  else if (config.deliveryDiscountEnabled && totalAmount >= config.deliveryDiscountAmount) {
    deliveryFee = originalFee * config.deliveryDiscountRate
    discountAmount = originalFee - deliveryFee
    console.log(
      `🚚 满额配送费折扣: 订单金额${totalAmount}元 >= 折扣门槛${config.deliveryDiscountAmount}元，折扣率${config.deliveryDiscountRate}`,
    )
  }

  const result = {
    deliveryFee: Math.round(deliveryFee * 100) / 100, // 保留2位小数
    originalFee: Math.round(originalFee * 100) / 100,
    discountAmount: Math.round(discountAmount * 100) / 100,
    isFree,
    freeThreshold: config.deliveryFreeEnabled ? config.deliveryFreeAmount : undefined,
    discountThreshold: config.deliveryDiscountEnabled ? config.deliveryDiscountAmount : undefined,
    discountRate: config.deliveryDiscountEnabled ? config.deliveryDiscountRate : undefined,
    distance: calculatedDistance,
  }

  console.log(`🚚 配送费计算结果:`, result)
  return result
}

/**
 * 批量计算多商家配送费
 * @param config 配送费配置
 * @param merchantGroups 商家分组数据
 * @param userLat 用户纬度（可选）
 * @param userLng 用户经度（可选）
 * @returns 每个商家的配送费计算结果
 */
export const calculateMultiMerchantDeliveryFee = (
  config: IDeliveryConfig,
  merchantGroups: Array<{
    merchantId: number
    selectedSubtotal: number
    merchantLatitude?: number
    merchantLongitude?: number
  }>,
  userLat?: number,
  userLng?: number,
): Array<{ merchantId: number; deliveryFeeResult: IDeliveryFeeCalculateResponse }> => {
  console.log(`🚚 开始批量计算配送费，商家数量: ${merchantGroups.length}`)
  console.log(`🚚 用户坐标: ${userLat}, ${userLng}`)

  return merchantGroups.map((group) => {
    console.log(`🚚 计算商家${group.merchantId}的配送费，订单金额: ${group.selectedSubtotal}`)
    console.log(`🚚 商家坐标: ${group.merchantLatitude}, ${group.merchantLongitude}`)

    const deliveryFeeResult = calculateDeliveryFee(
      config,
      group.selectedSubtotal,
      group.merchantId,
      undefined, // distance
      userLat,
      userLng,
      group.merchantLatitude,
      group.merchantLongitude,
    )

    return {
      merchantId: group.merchantId,
      deliveryFeeResult,
    }
  })
}

/**
 * 格式化配送费显示文本
 * @param result 配送费计算结果
 * @returns 格式化的显示文本
 */
export const formatDeliveryFeeText = (result: IDeliveryFeeCalculateResponse): string => {
  if (result.isFree) {
    return '免配送费'
  }

  if (result.discountAmount > 0) {
    return `¥${result.deliveryFee} (原价¥${result.originalFee})`
  }

  return `¥${result.deliveryFee}`
}

/**
 * 获取配送费优惠提示文本
 * @param config 配送费配置
 * @param currentAmount 当前金额
 * @returns 优惠提示文本
 */
export const getDeliveryFeeTip = (config: IDeliveryConfig, currentAmount: number): string => {
  // 检查是否已经满足免配送费条件
  if (config.deliveryFreeEnabled && currentAmount >= config.deliveryFreeAmount) {
    return '已享受免配送费优惠'
  }

  // 检查是否已经满足配送费折扣条件
  if (config.deliveryDiscountEnabled && currentAmount >= config.deliveryDiscountAmount) {
    const discountPercent = Math.round((1 - config.deliveryDiscountRate) * 100)
    return `已享受配送费${discountPercent}%折扣`
  }

  // 提示距离免配送费还差多少
  if (config.deliveryFreeEnabled && currentAmount < config.deliveryFreeAmount) {
    const diff = config.deliveryFreeAmount - currentAmount
    return `再购买¥${diff.toFixed(2)}即可免配送费`
  }

  // 提示距离配送费折扣还差多少
  if (config.deliveryDiscountEnabled && currentAmount < config.deliveryDiscountAmount) {
    const diff = config.deliveryDiscountAmount - currentAmount
    const discountPercent = Math.round((1 - config.deliveryDiscountRate) * 100)
    return `再购买¥${diff.toFixed(2)}即可享受配送费${discountPercent}%折扣`
  }

  return ''
}

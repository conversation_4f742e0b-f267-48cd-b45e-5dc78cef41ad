import {
  ILoginRequest,
  LoginRes,
  IRefreshTokenRequest,
  IRefreshResponse,
  IUpdateInfo,
  IUpdatePassword,
  InfoResponse,
} from './login.typings'
import request from '@/utils/request'

/**
 * 登录表单
 */
export interface ILoginForm {
  username: string
  password: string
}

/**
 * 用户登录
 * @param loginForm 登录表单
 */
export const login = (loginForm: ILoginForm) => {
  return request<LoginRes>('/api/v1/user/login', {
    method: 'POST',
    data: loginForm,
  })
}

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return request<InfoResponse>('/api/v1/user/secured/info', {
    method: 'GET',
  })
}

/**
 * 退出登录
 */
export const logout = () => {
  return request<void>('/api/v1/user/logout', {
    method: 'POST',
  })
}

/**
 * 修改用户信息
 */
export const updateInfo = (data: IUpdateInfo) => {
  return request('/api/v1/user/info', {
    method: 'PUT',
    data,
  })
}

/**
 * 修改用户密码
 */
export const updateUserPassword = (data: IUpdatePassword) => {
  return request('/api/v1/user/password', {
    method: 'PUT',
    data,
  })
}

/**
 * 获取微信登录凭证
 * @returns Promise 包含微信登录凭证(code)
 */
export const getWxCode = () => {
  return new Promise<UniApp.LoginRes>((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (res) => resolve(res),
      fail: (err) => reject(new Error(err)),
    })
  })
}

/**
 * 微信登录参数
 */

/**
 * 微信登录
 * @param params 微信登录参数，包含code
 * @returns Promise 包含登录结果
 */
export const wxLogin = (data: { code: string }) => {
  return request<LoginRes>('/api/v1/user/wx-login', {
    method: 'POST',
    data,
  })
}

/**
 * 刷新令牌
 * @param refreshToken 刷新令牌
 * @returns
 * 刷新令牌
 * 返回的data结构是直接的token信息，不再嵌token_info
 */
export const refreshToken = (params: IRefreshTokenRequest) => {
  return request<IRefreshResponse>('/api/v1/user/refresh-token', {
    method: 'POST',
    data: params,
  })
}

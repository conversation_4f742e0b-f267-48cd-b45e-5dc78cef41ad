import '@/style/index.scss'
import { VueQueryPlugin } from '@tanstack/vue-query'
import 'uno.css'
import { createSSRApp } from 'vue'

import App from './App.vue'
import { prototypeInterceptor, requestInterceptor, routeInterceptor } from './interceptors'
import store from './store'
import { applyAllTouchOptimizations, supportsPassiveEvents } from './utils/touchEventFixSimple'
import { autoFixTabBar, checkTabBarStatus, cleanupTabBarFixes } from './utils/simpleTabbarFix'
import { applyAllUniScrollViewFixes, checkScrollViewFixStatus } from './utils/uniScrollViewFix'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(VueQueryPlugin)

  // 修复触摸事件监听器的性能警告
  // 在应用启动时应用修复，确保第三方组件库的事件监听器被优化
  if (typeof window !== 'undefined' && supportsPassiveEvents()) {
    console.log('🔧 应用触摸事件优化修复...')

    // 立即应用 uni-app scroll-view 修复
    applyAllUniScrollViewFixes()

    // 应用其他触摸事件优化
    applyAllTouchOptimizations()

    // 修复 TabBar 固定定位问题 - 使用简单直接的方案
    setTimeout(() => {
      console.log('🔧 开始简单 TabBar 修复...')

      // 1. 清理之前可能的问题修复
      cleanupTabBarFixes()

      // 2. 应用简单修复并启动自动监听
      autoFixTabBar()

      // 3. 检查修复效果
      setTimeout(() => {
        const isWorking = checkTabBarStatus()
        if (isWorking) {
          console.log('✅ TabBar 修复成功！')
        } else {
          console.warn('⚠️ TabBar 修复可能未完全生效，请检查控制台信息')
        }

        // 4. 检查 scroll-view 修复效果
        const scrollViewFixed = checkScrollViewFixStatus()
        if (scrollViewFixed) {
          console.log('✅ scroll-view 修复已应用！')
        }
      }, 1000)
    }, 500) // 缩短延迟，更快应用修复
  }

  return {
    app,
  }
}

<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '消息列表',
    navigationStyle: 'custom',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="chat-list-page">
    <!-- 自定义导航栏 -->
    <wd-navbar title="消息" left-text="返回" left-arrow @click-left="handleBack">
      <template #right>
        <wd-icon name="add" size="24" @click="showServiceSelector = true" />
      </template>
    </wd-navbar>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索聊天记录"
        @search="handleSearch"
        @clear="handleClearSearch"
      />
    </view>

    <!-- 会话列表 -->
    <view class="conversation-list">
      <view v-if="filteredConversations.length > 0" class="list-content">
        <view
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          class="conversation-item"
          :class="{ top: conversation.isTop }"
          @click="handleConversationClick(conversation)"
          @longpress="handleLongPress(conversation)"
        >
          <!-- 头像 -->
          <view class="avatar-container">
            <Avatar :src="conversation.avatar" :size="96" :text="conversation.title.charAt(0)" />

            <!-- 在线状态 -->
            <view v-if="isUserOnline(conversation)" class="online-status" />

            <!-- 静音图标 -->
            <view v-if="conversation.isMuted" class="mute-icon">
              <wd-icon name="volume-off" size="16" color="#999" />
            </view>
          </view>

          <!-- 会话信息 -->
          <view class="conversation-info">
            <view class="conversation-header">
              <text class="conversation-title">{{ conversation.title }}</text>
              <text class="conversation-time">{{ formatTime(conversation.updatedAt) }}</text>
            </view>

            <view class="conversation-content">
              <view class="last-message">
                <text class="message-content">{{ getLastMessageText(conversation) }}</text>
              </view>

              <view class="conversation-badges">
                <!-- 未读数量 -->
                <wd-badge
                  v-if="conversation.unreadCount > 0 && !conversation.isMuted"
                  :value="conversation.unreadCount > 99 ? '99+' : conversation.unreadCount"
                  type="danger"
                />

                <!-- 静音状态下的红点 -->
                <view
                  v-else-if="conversation.unreadCount > 0 && conversation.isMuted"
                  class="muted-badge"
                />

                <!-- 置顶图标 -->
                <wd-icon v-if="conversation.isTop" name="pin" size="16" color="#ff4757" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <Empty image="/static/images/empty-chat.png" description="暂无聊天记录">
          <wd-button type="primary" @click="showServiceSelector = true">联系客服</wd-button>
        </Empty>
      </view>
    </view>

    <!-- 客服选择弹窗 -->
    <wd-popup v-model="showServiceSelector" position="bottom" :safe-area-inset-bottom="true">
      <view class="service-selector">
        <view class="selector-header">
          <text class="selector-title">选择服务</text>
          <wd-button type="text" @click="showServiceSelector = false">取消</wd-button>
        </view>

        <view class="service-list">
          <view class="service-item" @click="handleContactService('customer')">
            <wd-icon name="service" size="48" color="#1890ff" />
            <view class="service-info">
              <text class="service-name">在线客服</text>
              <text class="service-desc">7×24小时为您服务</text>
            </view>
            <wd-icon name="arrow-right" size="16" color="#999" />
          </view>

          <view class="service-item" @click="handleContactService('merchant')">
            <wd-icon name="shop" size="48" color="#52c41a" />
            <view class="service-info">
              <text class="service-name">商家客服</text>
              <text class="service-desc">联系商家解决问题</text>
            </view>
            <wd-icon name="arrow-right" size="16" color="#999" />
          </view>

          <view class="service-item" @click="handleContactService('delivery')">
            <wd-icon name="truck" size="48" color="#fa8c16" />
            <view class="service-info">
              <text class="service-name">配送客服</text>
              <text class="service-desc">配送相关问题咨询</text>
            </view>
            <wd-icon name="arrow-right" size="16" color="#999" />
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 长按操作菜单 -->
    <wd-action-sheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="handleActionSelect"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useChatStore } from '@/store/chat'
import { useWebSocketStore } from '@/store/websocket'
import { formatRelativeTime } from '@/utils/date'
import Avatar from '@/components/Avatar/index.vue'
import Empty from '@/components/Empty/index.vue'
import { IConversation, ConversationType } from '@/api/chat.typings'

const router = useRouter()
const chatStore = useChatStore()

// 搜索关键词
const searchKeyword = ref('')
// 弹窗状态
const showServiceSelector = ref(false)
const showActionSheet = ref(false)
// 当前操作的会话
const currentActionConversation = ref<IConversation | null>(null)

// 计算属性
const conversations = computed(() => chatStore.conversations)
const loading = computed(() => chatStore.loading.conversations)

// 过滤后的会话列表
const filteredConversations = computed(() => {
  if (!searchKeyword.value) {
    return conversations.value
  }

  return conversations.value.filter((conversation) => {
    return (
      conversation.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      getLastMessageText(conversation).toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  })
})

// 操作菜单选项
const actionSheetActions = computed(() => {
  if (!currentActionConversation.value) return []

  const conversation = currentActionConversation.value
  const actions = []

  // 置顶/取消置顶
  actions.push({
    name: conversation.isTop ? '取消置顶' : '置顶聊天',
    value: 'toggle-top',
  })

  // 静音/取消静音
  actions.push({
    name: conversation.isMuted ? '取消静音' : '消息免打扰',
    value: 'toggle-mute',
  })

  // 删除会话
  actions.push({
    name: '删除聊天',
    value: 'delete',
    color: '#ff4757',
  })

  return actions
})

// 获取最后一条消息的文本
const getLastMessageText = (conversation: IConversation) => {
  if (!conversation.lastMessage) {
    return '暂无消息'
  }

  const message = conversation.lastMessage
  switch (message.type) {
    case 'text':
      return message.content.text || ''
    case 'image':
      return '[图片]'
    case 'voice':
      return '[语音]'
    case 'video':
      return '[视频]'
    case 'file':
      return '[文件]'
    case 'location':
      return '[位置]'
    case 'order':
      return '[订单]'
    case 'goods':
      return '[商品]'
    case 'system':
      return message.content.text || '[系统消息]'
    default:
      return '[未知消息]'
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return formatRelativeTime(time)
}

// 判断用户是否在线
const isUserOnline = (conversation: IConversation) => {
  // 这里可以根据会话类型和参与者信息判断在线状态
  const otherParticipant = conversation.participants.find((p) => p.userType !== 'user')
  return otherParticipant?.isOnline || false
}

// 点击会话
const handleConversationClick = (conversation: IConversation) => {
  router.push({
    path: '/chat/detail',
    query: {
      conversationId: conversation.id,
    },
  })
}

// 长按会话
const handleLongPress = (conversation: IConversation) => {
  currentActionConversation.value = conversation
  showActionSheet.value = true
}

// 处理操作选择
const handleActionSelect = async (action: any) => {
  if (!currentActionConversation.value) return

  const conversation = currentActionConversation.value

  try {
    switch (action.value) {
      case 'toggle-top':
        // 这里调用置顶API
        conversation.isTop = !conversation.isTop
        uni.showToast({
          title: conversation.isTop ? '已置顶' : '已取消置顶',
          icon: 'success',
        })
        break

      case 'toggle-mute':
        // 这里调用静音API
        conversation.isMuted = !conversation.isMuted
        uni.showToast({
          title: conversation.isMuted ? '已开启免打扰' : '已关闭免打扰',
          icon: 'success',
        })
        break

      case 'delete':
        await handleDeleteConversation(conversation)
        break
    }
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }

  showActionSheet.value = false
  currentActionConversation.value = null
}

// 删除会话
const handleDeleteConversation = async (conversation: IConversation) => {
  const result = await uni.showModal({
    title: '删除聊天',
    content: '确定要删除这个聊天吗？删除后聊天记录将无法恢复。',
  })

  if (result.confirm) {
    try {
      await chatStore.removeConversation(conversation.id)
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
    } catch (error) {
      console.error('删除会话失败:', error)
      uni.showToast({
        title: '删除失败',
        icon: 'none',
      })
    }
  }
}

// 联系客服
const handleContactService = async (type: string) => {
  showServiceSelector.value = false

  try {
    let conversationType: ConversationType
    let participantId = ''

    switch (type) {
      case 'customer':
        conversationType = ConversationType.CUSTOMER_SERVICE
        participantId = 'customer_service'
        break
      case 'merchant':
        conversationType = ConversationType.MERCHANT
        participantId = 'merchant_service'
        break
      case 'delivery':
        conversationType = ConversationType.DELIVERY
        participantId = 'delivery_service'
        break
      default:
        return
    }

    // 检查是否已有相同类型的会话
    const existingConversation = conversations.value.find((conv) => conv.type === conversationType)

    if (existingConversation) {
      // 直接进入已有会话
      router.push({
        path: '/chat/detail',
        query: {
          conversationId: existingConversation.id,
        },
      })
    } else {
      // 创建新会话
      const newConversation = await chatStore.createNewConversation(conversationType, participantId)

      router.push({
        path: '/chat/detail',
        query: {
          conversationId: newConversation.id,
        },
      })
    }
  } catch (error) {
    console.error('联系客服失败:', error)
    uni.showToast({
      title: '联系客服失败',
      icon: 'none',
    })
  }
}

// 搜索
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = ''
}

// 下拉刷新
const onPullDownRefresh = async () => {
  try {
    await chatStore.fetchConversations()
    await chatStore.fetchUnreadCount()
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    uni.stopPullDownRefresh()
  }
}

/**
 * 返回主页
 */
const handleBack = () => {
  try {
    // 由于消息列表是tabbar页面，直接跳转到主页
    uni.switchTab({
      url: '/pages/index/index',
    })
  } catch (error) {
    console.error('返回主页失败:', error)
    uni.showToast({
      title: '返回失败',
      icon: 'none',
    })
  }
}

// 初始化数据
const initData = async () => {
  try {
    await chatStore.fetchConversations()
    await chatStore.fetchUnreadCount()
  } catch (error) {
    console.error('初始化聊天列表失败:', error)
  }
}

// WebSocket消息监听
const setupWebSocket = () => {
  // 导入WebSocket store
  const wsStore = useWebSocketStore()

  // 确保WebSocket连接已建立
  if (!wsStore.isConnected) {
    console.log('🔌 [ChatList] WebSocket未连接，正在初始化连接...')
    wsStore.initWebSocket()
  } else {
    console.log('✅ [ChatList] WebSocket已连接')
  }
}

onMounted(() => {
  initData()
  setupWebSocket()
})

onUnmounted(() => {
  // 清理WebSocket连接
})
</script>

<style lang="scss" scoped>
.chat-list-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-bar {
  padding: 20rpx;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.conversation-list {
  .list-content {
    background: white;
  }

  .conversation-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1px solid #f0f0f0;
    position: relative;

    &.top {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f0f0f0;
    }
  }

  .avatar-container {
    position: relative;
    margin-right: 24rpx;

    .online-status {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 20rpx;
      height: 20rpx;
      background-color: #52c41a;
      border: 2px solid white;
      border-radius: 50%;
    }

    .mute-icon {
      position: absolute;
      top: -8rpx;
      right: -8rpx;
      width: 32rpx;
      height: 32rpx;
      background-color: #999;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .conversation-info {
    flex: 1;
    min-width: 0;

    .conversation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8rpx;

      .conversation-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .conversation-time {
        font-size: 24rpx;
        color: #999;
        margin-left: 16rpx;
      }
    }

    .conversation-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .last-message {
        flex: 1;
        min-width: 0;

        .message-content {
          font-size: 28rpx;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .conversation-badges {
        display: flex;
        align-items: center;
        gap: 8rpx;
        margin-left: 16rpx;

        .muted-badge {
          width: 16rpx;
          height: 16rpx;
          background-color: #ff4757;
          border-radius: 50%;
        }
      }
    }
  }
}

.empty-state {
  padding: 120rpx 40rpx;
}

.service-selector {
  background: white;
  border-radius: 32rpx 32rpx 0 0;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    .selector-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .service-list {
    padding: 0 32rpx 32rpx;
  }

  .service-item {
    display: flex;
    align-items: center;
    padding: 32rpx 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f0f0f0;
    }

    .service-info {
      flex: 1;
      margin-left: 24rpx;

      .service-name {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
      }

      .service-desc {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}
</style>

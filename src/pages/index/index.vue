<!-- O_Mall用户端首页 - 商品浏览和搜索功能 -->
<route lang="json5" type="home">
{
  layout: 'tabbar',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: 'O_Mall',
  },
}
</route>
<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <wd-navbar title="O_Mall" />

    <!-- 搜索栏 -->
    <view class="search-bar">
      <wd-search v-model="searchKeyword" placeholder="搜索商家或美食" @search="onSearch" />
    </view>

    <!-- 轮播图 -->
    <swiper class="banner" indicator-dots circular autoplay>
      <swiper-item v-for="(banner, index) in banners" :key="index">
        <image :src="banner.image" mode="aspectFill" @click="onBannerClick(banner)" />
      </swiper-item>
    </swiper>

    <!-- 功能导航 -->
    <view class="function-nav">
      <view
        v-for="menu in appMenus"
        :key="menu.id"
        class="function-item"
        @click="onAppMenuClick(menu)"
      >
        <!-- 自定义图标 -->
        <image
          v-if="menu.iconType === 'custom' && menu.iconUrl"
          :src="menu.iconUrl"
          mode="aspectFit"
          class="function-icon"
        />
        <!-- 内置图标 -->
        <wd-icon
          v-else-if="menu.iconType === 'builtin' && menu.icon"
          :name="menu.icon"
          size="40px"
          class="function-icon"
        />
        <!-- 默认图标（兼容处理） -->
        <wd-icon v-else name="apps" size="40px" class="function-icon" />
        <text>{{ menu.title }}</text>
      </view>
    </view>

    <!-- 外卖推荐 -->
    <view class="takeout-section">
      <view class="section-title">
        <text>外卖推荐</text>
        <text class="more" @click="goToTakeout">更多商家</text>
      </view>

      <scroll-view
        scroll-x
        class="merchant-scroll"
        :show-scrollbar="false"
        :scroll-with-animation="true"
      >
        <view
          v-for="merchant in recommendMerchants"
          :key="merchant.id"
          class="merchant-card"
          @click="onMerchantClick(merchant)"
        >
          <image
            :src="merchant.logo || '/static/images/default-merchant.png'"
            mode="aspectFill"
            class="merchant-image"
          />
          <view class="merchant-info">
            <text class="merchant-name">{{ merchant.name }}</text>
            <view class="merchant-rating">
              <wd-rate :value="0" size="12" readonly />
              <text>暂无评分</text>
            </view>
            <view class="merchant-delivery">
              <text>{{ '营业中' }}</text>
              <text class="divider">|</text>
              <text>{{ merchant.description || '暂无描述' }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 外卖全局分类导航 -->
    <view class="category-section">
      <view class="section-title">
        <text>美食分类</text>
        <text class="more" @click="goToTakeout">查看全部</text>
      </view>

      <scroll-view
        scroll-x
        class="category-scroll"
        :show-scrollbar="false"
        :scroll-with-animation="true"
      >
        <view class="category-list">
          <view
            v-for="category in globalCategories"
            :key="category.id"
            class="category-item"
            @click="onCategoryClick(category)"
          >
            <image
              :src="category.icon || '/static/images/default-category.png'"
              mode="aspectFit"
              class="category-icon"
            />
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 热门美食 -->
    <view class="food-section">
      <view class="section-title">
        <text>热门美食</text>
        <text class="more" @click="goToTakeout">查看全部</text>
      </view>

      <view class="food-grid">
        <view v-for="food in hotFoods" :key="food.id" class="food-card" @click="onFoodClick(food)">
          <image :src="food.image" mode="aspectFill" class="food-image" />
          <view class="food-info">
            <text class="food-name">{{ food.name }}</text>
            <view class="food-price">
              <text class="current-price">¥{{ food.price.toFixed(2) }}</text>
              <text
                v-if="food.original_price && food.original_price > food.price"
                class="original-price"
              >
                ¥{{ food.original_price.toFixed(2) }}
              </text>
            </view>
            <view class="food-stats">
              <text class="sales">月售{{ food.sales_count }}</text>
              <view v-if="food.rating > 0" class="rating">
                <wd-rate :value="food.rating" size="12" readonly />
                <text>{{ food.rating.toFixed(1) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <wd-loading />
        <!-- <wd-loadmore state="loading" /> -->
      </view>

      <!-- 无数据状态 -->
      <view v-if="!loading && hotFoods.length === 0" class="empty-state">
        <image src="/static/images/empty.png" mode="aspectFit" />
        <text>暂无美食推荐</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { onReachBottom, onShow } from '@dcloudio/uni-app'
import { useTakeoutStore } from '@/store/takeout'
import { useSystemStore } from '@/store/system'
import { getRecommendedMerchants } from '@/api/takeout'
import type { ITakeoutMerchant, ITakeoutFood } from '@/api/takeout.typings'
import type { IAppMenuInfo, IGlobalCategory } from '@/api/system.typings'

const takeoutStore = useTakeoutStore()
const systemStore = useSystemStore()

// 搜索关键词
const searchKeyword = ref('')

// 轮播图数据 - 使用系统配置的轮播图
const banners = computed(() => {
  return systemStore.bannerList.map((banner) => ({
    id: banner.id,
    image: banner.imageUrl,
    title: banner.title,
    url: banner.linkUrl || '/pages/takeout/merchant-list',
  }))
})

// 功能导航数据 - 使用系统配置的功能导航
const appMenus = computed(() => {
  return systemStore.activeAppMenuList
})

// 外卖全局分类数据 - 使用系统配置的外卖全局分类
const globalCategories = computed(() => {
  return systemStore.primaryGlobalCategories
})

// 推荐商家数据
const recommendMerchants = ref<ITakeoutMerchant[]>([])

// 热门美食数据
const hotFoods = ref<ITakeoutFood[]>([])
const loading = ref(false)

// 搜索
const onSearch = () => {
  console.log('搜索:', searchKeyword.value)
  uni.navigateTo({
    url: `/pages/takeout/merchant-list?keyword=${encodeURIComponent(searchKeyword.value)}`,
  })
}

// 轮播图点击
const onBannerClick = (banner: any) => {
  console.log('轮播图点击:', banner)
  if (banner.url) {
    // 如果是外部链接，使用webview打开
    if (banner.url.startsWith('http')) {
      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(banner.url)}&title=${encodeURIComponent(banner.title || '详情')}`,
      })
    } else {
      // 内部页面跳转
      uni.navigateTo({ url: banner.url })
    }
  }
}

// 功能导航点击处理
const onAppMenuClick = (menu: IAppMenuInfo) => {
  console.log('功能导航点击:', menu)

  const linkUrl = (menu as any).linkUrl
  if (linkUrl) {
    // 如果是外部链接，使用webview打开
    if (linkUrl.startsWith('http')) {
      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(linkUrl)}&title=${encodeURIComponent(menu.title || '详情')}`,
      })
    } else {
      // 内部页面跳转
      uni.navigateTo({ url: linkUrl })
    }
  } else {
    // 如果没有配置链接，显示提示
    uni.showToast({
      title: `${menu.title}功能开发中`,
      icon: 'none',
    })
  }
}

// 跳转到外卖页面
const goToTakeout = () => {
  uni.navigateTo({
    url: '/pages/takeout/merchant-list',
  })
}

// 商家点击
const onMerchantClick = (merchant: ITakeoutMerchant) => {
  console.log('商家点击:', merchant)

  // 将商家数据存储到store中
  takeoutStore.setCurrentMerchant(merchant)

  uni.navigateTo({
    url: `/pages/takeout/merchant-detail?id=${merchant.id}&fromList=1`,
  })
}

// 美食点击
const onFoodClick = (food: ITakeoutFood) => {
  console.log('美食点击:', food)
  // 跳转到对应商家页面
  uni.navigateTo({
    url: `/pages/takeout/merchant-detail?id=${food.merchant_id}`,
  })
}

// 分类点击
const onCategoryClick = (category: IGlobalCategory) => {
  console.log('分类点击:', category)
  // 跳转到外卖商家列表页面，并传递分类ID
  uni.navigateTo({
    url: `/pages/takeout/merchant-list?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`,
  })
}

// 加载推荐商家
const loadRecommendMerchants = async (retryCount = 0) => {
  try {
    // 调用真实API获取推荐商家
    const response = await getRecommendedMerchants({ page: 1, pageSize: 10 })
    console.log('获取到的推荐商家数据:', response)
    
    // 确保数据结构正确
    if (response && Array.isArray(response.list)) {
      recommendMerchants.value = response.list
    } else if (response && Array.isArray(response)) {
      recommendMerchants.value = response
    } else {
      console.warn('推荐商家数据格式异常:', response)
      recommendMerchants.value = []
    }
  } catch (error) {
    console.error('加载推荐商家失败:', error)
    
    // 重试机制：最多重试2次
    if (retryCount < 2) {
      console.log(`推荐商家加载失败，正在重试... (${retryCount + 1}/2)`)
      setTimeout(() => {
        loadRecommendMerchants(retryCount + 1)
      }, 1000 * (retryCount + 1)) // 递增延迟
    } else {
      uni.showToast({
        title: '加载推荐商家失败',
        icon: 'none',
      })
      // 设置空数组避免界面异常
      recommendMerchants.value = []
    }
  }
}

// 加载热门美食
const loadHotFoods = async () => {
  if (loading.value) return

  loading.value = true

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 800))

    hotFoods.value = [
      {
        id: 1,
        merchant_id: 1,
        name: '巨无霸套餐',
        description: '经典巨无霸汉堡+薯条+可乐',
        brief: '经典巨无霸汉堡+薯条+可乐',
        image: '/static/images/food1.jpg',
        price: 35.5,
        original_price: 42,
        packaging_fee: 2,
        preparation_time: 15,
        is_spicy: false,
        is_combination: true,
        has_variants: false,
        sold_out: false,
        min_price: 35.5,
        max_price: 35.5,
        daily_limit: 100,
        total_sold: 1234,
        tags: ['热销', '套餐'],
        category_id: 1,
        category_name: '套餐',
        status: 1,
        audit_status: 1,
        is_recommend: true,
        variants: [],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        merchant_name: '麦当劳',
        sales_count: 1234,
        rating: 4.6,
        is_available: true,
        is_recommended: true,
      },
      {
        id: 2,
        merchant_id: 2,
        name: '香辣鸡腿堡',
        description: '酥脆鸡腿肉配特制香辣酱',
        brief: '酥脆鸡腿肉配特制香辣酱',
        image: '/static/images/food2.jpg',
        price: 18.5,
        original_price: null,
        packaging_fee: 2,
        preparation_time: 12,
        is_spicy: true,
        is_combination: false,
        has_variants: false,
        sold_out: false,
        min_price: 18.5,
        max_price: 18.5,
        daily_limit: 100,
        total_sold: 987,
        tags: ['香辣', '鸡肉'],
        category_id: 1,
        category_name: '汉堡',
        status: 1,
        audit_status: 1,
        is_recommend: false,
        variants: [],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        merchant_name: '肯德基',
        sales_count: 987,
        rating: 4.4,
        is_available: true,
        is_recommended: false,
      },
      {
        id: 3,
        merchant_id: 3,
        name: '美式咖啡',
        description: '精选阿拉比卡咖啡豆',
        brief: '精选阿拉比卡咖啡豆',
        image: '/static/images/food3.jpg',
        price: 28,
        original_price: null,
        packaging_fee: 1,
        preparation_time: 5,
        is_spicy: false,
        is_combination: false,
        has_variants: false,
        sold_out: false,
        min_price: 28,
        max_price: 28,
        daily_limit: 200,
        total_sold: 2156,
        tags: ['咖啡', '经典'],
        category_id: 2,
        category_name: '饮品',
        status: 1,
        audit_status: 1,
        is_recommend: true,
        variants: [],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        merchant_name: '星巴克',
        sales_count: 2156,
        rating: 4.8,
        is_available: true,
        is_recommended: true,
      },
      {
        id: 4,
        merchant_id: 1,
        name: '麦辣鸡翅',
        description: '香辣可口的炸鸡翅',
        brief: '香辣可口的炸鸡翅',
        image: '/static/images/food4.jpg',
        price: 12.5,
        original_price: 15,
        packaging_fee: 1,
        preparation_time: 8,
        is_spicy: true,
        is_combination: false,
        has_variants: false,
        sold_out: false,
        min_price: 12.5,
        max_price: 12.5,
        daily_limit: 150,
        total_sold: 756,
        tags: ['香辣', '小食'],
        category_id: 3,
        category_name: '小食',
        status: 1,
        audit_status: 1,
        is_recommend: false,
        variants: [],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        merchant_name: '麦当劳',
        sales_count: 756,
        rating: 4.2,
        is_available: true,
        is_recommended: false,
      },
    ]
  } catch (error) {
    console.error('加载热门美食失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 页面触底
onReachBottom(() => {
  // 可以在这里加载更多数据
})

// 页面显示时刷新数据
onShow(() => {
  // 刷新推荐商家数据，确保数据最新
  loadRecommendMerchants()
})

// 页面加载
onMounted(async () => {
  // 加载系统配置数据
  try {
    await Promise.all([
      systemStore.getBannerList(),
      systemStore.getAppMenuList(),
      systemStore.getGlobalCategoryList(),
    ])
  } catch (error) {
    console.error('加载系统配置失败:', error)
  }

  loadRecommendMerchants()
  loadHotFoods()
})
</script>

<style lang="scss">
.container {
  padding-bottom: 20px;
  background-color: #f5f5f5;
}

.search-bar {
  padding: 10px 15px;
  background-color: #fff;
}

.banner {
  height: 180px;
  margin-bottom: 10px;

  image {
    width: 100%;
    height: 100%;
  }
}

.function-nav {
  display: flex;
  padding: 20px 15px;
  background-color: #fff;
  margin-bottom: 10px;

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;

    .function-icon {
      width: 40px;
      height: 40px;
      margin-bottom: 8px;
    }

    text {
      font-size: 12px;
      color: #333;
    }
  }
}

.takeout-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    text {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .more {
      font-size: 14px;
      color: #ff5500;
    }
  }

  .merchant-scroll {
    white-space: nowrap;
  }

  .merchant-card {
    display: inline-block;
    width: 200px;
    margin-right: 10px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    vertical-align: top;

    &:last-child {
      margin-right: 0;
    }

    .merchant-image {
      width: 100%;
      height: 100px;
    }

    .merchant-info {
      padding: 10px;

      .merchant-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .merchant-rating {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        text {
          margin-left: 3px;
          font-size: 12px;
          color: #ff9500;
        }
      }

      .merchant-delivery {
        font-size: 12px;
        color: #666;

        .divider {
          margin: 0 5px;
          color: #ddd;
        }
      }
    }
  }
}

.category-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    text {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .more {
      font-size: 14px;
      color: #ff5500;
    }
  }

  .category-scroll {
    white-space: nowrap;
  }

  .category-list {
    display: flex;
    padding: 0;
  }

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 70px;
    margin-right: 20px;
    padding: 10px 8px;
    border-radius: 8px;
    transition: background-color 0.2s;
    flex-shrink: 0;

    &:last-child {
      margin-right: 15px;
    }

    &:active {
      background-color: #f5f5f5;
    }

    .category-icon {
      width: 40px;
      height: 40px;
      margin-bottom: 8px;
      border-radius: 50%;
      background-color: #f8f8f8;
    }

    .category-name {
      font-size: 12px;
      color: #333;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 60px;
    }
  }
}

.food-section {
  padding: 15px;
  background-color: #fff;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    text {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .more {
      font-size: 14px;
      color: #ff5500;
    }
  }

  .food-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .food-card {
    width: 48%;
    margin-bottom: 15px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .food-image {
      width: 100%;
      height: 120px;
    }

    .food-info {
      padding: 10px;

      .food-name {
        font-size: 14px;
        color: #333;
        margin-bottom: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .food-price {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        .current-price {
          font-size: 16px;
          font-weight: 500;
          color: #ff5500;
        }

        .original-price {
          margin-left: 5px;
          font-size: 12px;
          color: #999;
          text-decoration: line-through;
        }
      }

      .food-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .sales {
          font-size: 12px;
          color: #999;
        }

        .rating {
          display: flex;
          align-items: center;

          text {
            margin-left: 2px;
            font-size: 12px;
            color: #ff9500;
          }
        }
      }
    }
  }
}

.loading,
.empty-state {
  padding: 20px 0;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;

  image {
    width: 120px;
    height: 120px;
    margin-bottom: 10px;
  }

  text {
    font-size: 14px;
    color: #999;
  }
}
</style>

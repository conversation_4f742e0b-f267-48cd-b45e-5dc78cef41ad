<route lang="json5" type="tabbar">
{
  style: {
    navigationBarTitleText: '消息',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="message-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索消息"
        @search="handleSearch"
        @clear="handleClearSearch"
      />
    </view>

    <!-- 消息分类 -->
    <view class="message-categories">
      <view
        v-for="category in messageCategories"
        :key="category.type"
        class="category-item"
        @click="goToCategory(category)"
      >
        <view class="category-icon">
          <wd-icon :name="category.icon" :size="24" :color="category.color" />
          <wd-badge v-if="category.unreadCount > 0" :value="category.unreadCount" />
        </view>
        <text class="category-title">{{ category.title }}</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-list">
      <!-- 加载状态 -->
      <wd-loading v-if="loading" :size="24" color="#4D8EFF">加载中...</wd-loading>

      <!-- 空状态 -->
      <view v-else-if="filteredMessages.length === 0" class="empty-state">
        <view class="empty-icon">
          <wd-icon name="message" :size="60" color="#ddd" />
        </view>
        <text class="empty-text">{{ searchKeyword ? '暂无搜索结果' : '暂无消息' }}</text>
      </view>

      <!-- 消息项 -->
      <view v-else class="list-content">
        <view
          v-for="message in filteredMessages"
          :key="message.id"
          class="message-item"
          @click="goToChat(message)"
        >
          <view class="message-avatar">
            <image v-if="message.avatar" :src="message.avatar" mode="aspectFill" />
            <view v-else class="default-avatar">
              <text>{{ message.title.charAt(0) }}</text>
            </view>
            <wd-badge v-if="message.unreadCount > 0" :value="message.unreadCount" />
          </view>

          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{ message.title }}</text>
              <text class="message-time">{{ formatTime(message.lastTime) }}</text>
            </view>

            <view class="message-preview">
              <text class="preview-text">{{ getPreviewText(message) }}</text>
              <wd-icon v-if="message.isMuted" name="volume-off" :size="14" color="#999" />
            </view>
          </view>

          <view v-if="message.isPinned" class="pin-icon">
            <wd-icon name="pin" :size="16" color="#ff6b35" />
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <wd-loadmore v-if="filteredMessages.length > 0" :state="loadMoreState" @loadmore="loadMore" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 消息类型定义
interface MessageItem {
  id: string
  type: 'chat' | 'system' | 'order' | 'service'
  title: string
  avatar?: string
  lastMessage: string
  lastTime: number
  unreadCount: number
  isPinned: boolean
  isMuted: boolean
}

interface MessageCategory {
  type: string
  title: string
  icon: string
  color: string
  unreadCount: number
  path: string
}

// 响应式数据
const searchKeyword = ref('')
const loading = ref(false)
const messageList = ref<MessageItem[]>([])
const pageNo = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)

// 消息分类
const messageCategories = ref<MessageCategory[]>([
  {
    type: 'chat',
    title: '聊天消息',
    icon: 'chat',
    color: '#4D8EFF',
    unreadCount: 3,
    path: '/pages/chat/sessions/index',
  },
  {
    type: 'system',
    title: '系统通知',
    icon: 'notification',
    color: '#FF9500',
    unreadCount: 2,
    path: '/pages/message/system',
  },
  {
    type: 'order',
    title: '订单消息',
    icon: 'order',
    color: '#34C759',
    unreadCount: 1,
    path: '/pages/message/order',
  },
  {
    type: 'service',
    title: '客服消息',
    icon: 'service',
    color: '#FF3B30',
    unreadCount: 0,
    path: '/pages/message/service',
  },
])

// 计算属性
const filteredMessages = computed(() => {
  if (!searchKeyword.value) {
    return messageList.value
  }

  return messageList.value.filter(
    (message) =>
      message.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      message.lastMessage.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'finished'
  return 'more'
})

// 方法
const loadMessages = async (refresh = false) => {
  if (loading.value) return

  try {
    loading.value = true

    if (refresh) {
      pageNo.value = 1
      messageList.value = []
    }

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const mockMessages: MessageItem[] = [
      {
        id: '1',
        type: 'chat',
        title: '张三',
        avatar: 'https://via.placeholder.com/60',
        lastMessage: '好的，我知道了',
        lastTime: Date.now() - 5 * 60 * 1000,
        unreadCount: 2,
        isPinned: true,
        isMuted: false,
      },
      {
        id: '2',
        type: 'system',
        title: '系统通知',
        lastMessage: '您的订单已发货，请注意查收',
        lastTime: Date.now() - 2 * 60 * 60 * 1000,
        unreadCount: 1,
        isPinned: false,
        isMuted: false,
      },
      {
        id: '3',
        type: 'order',
        title: '订单消息',
        lastMessage: '您的订单已完成，请对商品进行评价',
        lastTime: Date.now() - 1 * 24 * 60 * 60 * 1000,
        unreadCount: 0,
        isPinned: false,
        isMuted: true,
      },
      {
        id: '4',
        type: 'service',
        title: '客服小助手',
        avatar: 'https://via.placeholder.com/60',
        lastMessage: '有什么可以帮助您的吗？',
        lastTime: Date.now() - 3 * 24 * 60 * 60 * 1000,
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
      },
    ]

    if (refresh) {
      messageList.value = mockMessages
    } else {
      messageList.value.push(...mockMessages)
    }

    hasMore.value = mockMessages.length === pageSize.value
  } catch (error) {
    console.error('加载消息列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleClearSearch = () => {
  searchKeyword.value = ''
}

const goToCategory = (category: MessageCategory) => {
  uni.navigateTo({
    url: category.path,
  })
}

const goToChat = (message: MessageItem) => {
  // 根据消息类型跳转到不同页面
  switch (message.type) {
    case 'chat':
      uni.navigateTo({
        url: `/pages/chat/room/index?id=${message.id}&type=private`,
      })
      break
    case 'system':
      uni.navigateTo({
        url: `/pages/message/system?id=${message.id}`,
      })
      break
    case 'order':
      uni.navigateTo({
        url: `/pages/message/order?id=${message.id}`,
      })
      break
    case 'service':
      uni.navigateTo({
        url: `/pages/message/service?id=${message.id}`,
      })
      break
  }

  // 标记为已读
  message.unreadCount = 0
}

const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    const date = new Date(timestamp)
    return `${date.getMonth() + 1}/${date.getDate()}`
  }
}

const getPreviewText = (message: MessageItem) => {
  if (message.type === 'system') {
    return `[系统] ${message.lastMessage}`
  } else if (message.type === 'order') {
    return `[订单] ${message.lastMessage}`
  } else if (message.type === 'service') {
    return `[客服] ${message.lastMessage}`
  }
  return message.lastMessage
}

const loadMore = () => {
  if (!hasMore.value || loading.value) return

  pageNo.value++
  loadMessages()
}

// 生命周期
onMounted(() => {
  loadMessages(true)
})
</script>

<style lang="scss" scoped>
.message-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-bar {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1px solid #eee;
}

.message-categories {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .category-icon {
      position: relative;
      margin-bottom: 16rpx;
    }

    .category-title {
      font-size: 24rpx;
      color: #666;
      text-align: center;
    }
  }
}

.message-list {
  padding: 0 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    margin-bottom: 32rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.list-content {
  .message-item {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .message-avatar {
      position: relative;
      width: 96rpx;
      height: 96rpx;
      margin-right: 24rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .default-avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #4d8eff;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 32rpx;
          color: #fff;
          font-weight: 500;
        }
      }
    }

    .message-content {
      flex: 1;

      .message-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .message-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
        }

        .message-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .message-preview {
        display: flex;
        align-items: center;

        .preview-text {
          flex: 1;
          font-size: 28rpx;
          color: #666;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          margin-right: 12rpx;
        }
      }
    }

    .pin-icon {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
    }
  }
}
</style>

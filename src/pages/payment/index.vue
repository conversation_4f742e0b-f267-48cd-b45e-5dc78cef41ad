<template>
  <view class="payment-page">
    <!-- 自定义导航栏 -->
    <wd-navbar title="收银台" left-text="返回" left-arrow @click-left="handleBack" />

    <!-- 订单信息 -->
    <view class="order-info">
      <view class="order-header">
        <text class="order-title">订单信息</text>
        <text class="order-no">订单号：{{ orderInfo.orderNo }}</text>
      </view>

      <view class="order-amount">
        <text class="amount-label">支付金额</text>
        <text class="amount-value">¥{{ orderInfo.totalAmount }}</text>
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-methods">
      <view class="section-title">选择支付方式</view>

      <wd-cell-group>
        <wd-cell
          v-for="method in availablePaymentMethods"
          :key="method.method"
          :title="method.name"
          :label="method.description"
          clickable
          @click="selectPaymentMethod(method)"
        >
          <template #icon>
            <image class="payment-icon" :src="method.icon" />
          </template>

          <template #right-icon>
            <wd-radio
              :model-value="selectedMethod?.method === method.method"
              @change="selectPaymentMethod(method)"
            />
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <!-- 余额支付详情 -->
    <view v-if="selectedMethod?.method === 'balance'" class="balance-info">
      <wd-cell-group>
        <wd-cell title="当前余额" :value="`¥${balanceInfo?.availableBalance || 0}`" />
        <wd-cell
          v-if="balanceInfo && balanceInfo.availableBalance < orderInfo.totalAmount"
          title="余额不足"
          value="请选择其他支付方式"
          label-class="error-text"
        />
      </wd-cell-group>
    </view>

    <!-- 银行卡支付详情 -->
    <view v-if="selectedMethod?.method === 'credit_card'" class="bank-card-info">
      <wd-cell-group>
        <wd-cell
          v-if="defaultBankCard"
          :title="defaultBankCard.bankName"
          :value="`**** **** **** ${defaultBankCard.cardNo.slice(-4)}`"
          clickable
          @click="showBankCardSelector = true"
        >
          <template #right-icon>
            <wd-icon name="arrow-right" />
          </template>
        </wd-cell>

        <wd-cell
          v-else
          title="选择银行卡"
          value="请添加银行卡"
          clickable
          @click="handleAddBankCard"
        >
          <template #right-icon>
            <wd-icon name="arrow-right" />
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <!-- 优惠信息 -->
    <view class="discount-info">
      <wd-cell-group>
        <wd-cell
          title="优惠券"
          :value="selectedCoupon ? `已选择 ${selectedCoupon.name}` : '选择优惠券'"
          clickable
          @click="showCouponSelector = true"
        >
          <template #right-icon>
            <wd-icon name="arrow-right" />
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <!-- 支付详情 -->
    <view class="payment-detail">
      <view class="detail-item">
        <text class="detail-label">商品金额</text>
        <text class="detail-value">¥{{ orderInfo.goodsAmount }}</text>
      </view>

      <view class="detail-item">
        <text class="detail-label">运费</text>
        <text class="detail-value">¥{{ orderInfo.shippingFee }}</text>
      </view>

      <view v-if="selectedCoupon" class="detail-item">
        <text class="detail-label">优惠券</text>
        <text class="detail-value discount">-¥{{ selectedCoupon.amount }}</text>
      </view>

      <view class="detail-item total">
        <text class="detail-label">实付金额</text>
        <text class="detail-value">¥{{ finalAmount }}</text>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-footer">
      <wd-button
        type="primary"
        size="large"
        :loading="paymentLoading"
        :disabled="!canPay"
        @click="handlePay"
      >
        {{ paymentLoading ? '支付中...' : `立即支付 ¥${finalAmount}` }}
      </wd-button>
    </view>

    <!-- 银行卡选择弹窗 -->
    <wd-popup v-model="showBankCardSelector" position="bottom" :safe-area-inset-bottom="true">
      <view class="bank-card-selector">
        <view class="selector-header">
          <text class="selector-title">选择银行卡</text>
          <wd-button type="text" @click="showBankCardSelector = false">取消</wd-button>
        </view>

        <view class="bank-card-list">
          <view
            v-for="card in bankCards"
            :key="card.id"
            class="bank-card-item"
            :class="{ active: selectedBankCard?.id === card.id }"
            @click="selectBankCard(card)"
          >
            <view class="card-info">
              <text class="bank-name">{{ card.bankName }}</text>
              <text class="card-no">**** **** **** {{ card.cardNo.slice(-4) }}</text>
            </view>

            <wd-radio :model-value="selectedBankCard?.id === card.id" />
          </view>
        </view>

        <view class="selector-footer">
          <wd-button type="text" @click="handleAddBankCard">添加新银行卡</wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- 优惠券选择弹窗 -->
    <wd-popup v-model="showCouponSelector" position="bottom" :safe-area-inset-bottom="true">
      <view class="coupon-selector">
        <view class="selector-header">
          <text class="selector-title">选择优惠券</text>
          <wd-button type="text" @click="showCouponSelector = false">取消</wd-button>
        </view>

        <view class="coupon-list">
          <view
            class="coupon-item"
            :class="{ active: !selectedCoupon }"
            @click="selectCoupon(null)"
          >
            <text class="coupon-text">不使用优惠券</text>
            <wd-radio :model-value="!selectedCoupon" />
          </view>

          <view
            v-for="coupon in availableCoupons"
            :key="coupon.id"
            class="coupon-item"
            :class="{ active: selectedCoupon?.id === coupon.id }"
            @click="selectCoupon(coupon)"
          >
            <view class="coupon-info">
              <text class="coupon-name">{{ coupon.name }}</text>
              <text class="coupon-desc">{{ coupon.description }}</text>
            </view>

            <view class="coupon-amount">
              <text class="amount">¥{{ coupon.amount }}</text>
              <wd-radio :model-value="selectedCoupon?.id === coupon.id" />
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 支付密码弹窗 -->
    <wd-popup v-model="showPasswordInput" position="center">
      <view class="password-input">
        <view class="password-header">
          <text class="password-title">请输入支付密码</text>
          <text class="password-amount">¥{{ finalAmount }}</text>
        </view>

        <wd-password-input
          v-model="paymentPassword"
          :length="6"
          @complete="handlePasswordComplete"
        />

        <view class="password-footer">
          <wd-button type="text" @click="showPasswordInput = false">取消</wd-button>
          <wd-button type="text" @click="handleForgotPassword">忘记密码？</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePaymentStore } from '@/store/payment'
import { useOrderStore } from '@/store/order'
import { createPayment, queryPayment } from '@/api/payment'
import type { IPaymentMethodConfig, IBankCard } from '@/api/payment.typings'

const router = useRouter()
const route = useRoute()
const paymentStore = usePaymentStore()
const orderStore = useOrderStore()

// 订单信息
const orderInfo = ref({
  orderNo: (route.query.orderNo as string) || '',
  totalAmount: Number(route.query.amount) || 0,
  goodsAmount: Number(route.query.goodsAmount) || 0,
  shippingFee: Number(route.query.shippingFee) || 0,
})

// 支付相关状态
const selectedMethod = ref<IPaymentMethodConfig | null>(null)
const selectedBankCard = ref<IBankCard | null>(null)
const selectedCoupon = ref<any>(null)
const availableCoupons = ref<any[]>([])
const paymentLoading = ref(false)
const paymentPassword = ref('')

// 弹窗状态
const showBankCardSelector = ref(false)
const showCouponSelector = ref(false)
const showPasswordInput = ref(false)

// 计算属性
const availablePaymentMethods = computed(() => paymentStore.availablePaymentMethods)
const balanceInfo = computed(() => paymentStore.balanceInfo)
const bankCards = computed(() => paymentStore.bankCards)
const defaultBankCard = computed(() => paymentStore.defaultBankCard)

// 最终支付金额
const finalAmount = computed(() => {
  let amount = orderInfo.value.totalAmount
  if (selectedCoupon.value) {
    amount -= selectedCoupon.value.amount
  }
  return Math.max(0, amount)
})

// 是否可以支付
const canPay = computed(() => {
  if (!selectedMethod.value) return false

  if (selectedMethod.value.method === 'balance') {
    return balanceInfo.value && balanceInfo.value.availableBalance >= finalAmount.value
  }

  if (selectedMethod.value.method === 'credit_card') {
    return selectedBankCard.value !== null
  }

  return true
})

// 选择支付方式
const selectPaymentMethod = (method: IPaymentMethodConfig) => {
  selectedMethod.value = method

  // 如果选择银行卡支付，自动选择默认银行卡
  if (method.method === 'credit_card' && defaultBankCard.value) {
    selectedBankCard.value = defaultBankCard.value
  }
}

// 选择银行卡
const selectBankCard = (card: IBankCard) => {
  selectedBankCard.value = card
  showBankCardSelector.value = false
}

// 选择优惠券
const selectCoupon = (coupon: any) => {
  selectedCoupon.value = coupon
  showCouponSelector.value = false
}

// 处理支付
const handlePay = async () => {
  if (!selectedMethod.value) {
    uni.showToast({
      title: '请选择支付方式',
      icon: 'none',
    })
    return
  }

  // 余额支付需要输入密码
  if (selectedMethod.value.method === 'balance') {
    showPasswordInput.value = true
    return
  }

  // 其他支付方式直接调用支付
  await processPayment()
}

// 处理密码输入完成
const handlePasswordComplete = async () => {
  showPasswordInput.value = false
  await processPayment()
}

// 执行支付
const processPayment = async () => {
  try {
    paymentLoading.value = true

    const paymentParams = {
      orderNo: orderInfo.value.orderNo,
      amount: finalAmount.value,
      paymentMethod: selectedMethod.value!.method,
      couponId: selectedCoupon.value?.id,
      bankCardId: selectedBankCard.value?.id,
      password: paymentPassword.value,
    }

    const result = await createPayment(paymentParams)

    if (result.code === 0) {
      // 根据支付方式处理不同的支付流程
      await handlePaymentResult(result.data)
    } else {
      uni.showToast({
        title: result.message || '支付失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('支付失败:', error)
    uni.showToast({
      title: '支付失败',
      icon: 'none',
    })
  } finally {
    paymentLoading.value = false
    paymentPassword.value = ''
  }
}

// 处理支付结果
const handlePaymentResult = async (paymentData: any) => {
  const { paymentId, paymentUrl, qrCode, paymentParams } = paymentData

  if (selectedMethod.value?.method === 'balance') {
    // 余额支付直接成功
    router.replace({
      path: '/payment/result',
      query: {
        paymentId,
        status: 'success',
      },
    })
  } else if (selectedMethod.value?.method === 'wechat') {
    // 微信支付
    await handleWechatPay(paymentParams)
  } else if (selectedMethod.value?.method === 'alipay') {
    // 支付宝支付
    await handleAlipay(paymentParams)
  } else {
    // 其他支付方式跳转到支付页面
    if (paymentUrl) {
      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(paymentUrl)}`,
      })
    }
  }
}

// 处理微信支付
const handleWechatPay = async (paymentParams: any) => {
  try {
    // 调用微信支付API
    await uni.requestPayment({
      provider: 'wxpay',
      ...paymentParams,
    })

    // 支付成功
    router.replace({
      path: '/payment/result',
      query: {
        paymentId: paymentParams.paymentId,
        status: 'success',
      },
    })
  } catch (error) {
    console.error('微信支付失败:', error)
    uni.showToast({
      title: '支付失败',
      icon: 'none',
    })
  }
}

// 处理支付宝支付
const handleAlipay = async (paymentParams: any) => {
  try {
    // 调用支付宝支付API
    await uni.requestPayment({
      provider: 'alipay',
      ...paymentParams,
    })

    // 支付成功
    router.replace({
      path: '/payment/result',
      query: {
        paymentId: paymentParams.paymentId,
        status: 'success',
      },
    })
  } catch (error) {
    console.error('支付宝支付失败:', error)
    uni.showToast({
      title: '支付失败',
      icon: 'none',
    })
  }
}

// 添加银行卡
const handleAddBankCard = () => {
  router.push('/payment/bank-card/add')
}

// 忘记密码
const handleForgotPassword = () => {
  router.push('/payment/password/reset')
}

// 返回
const handleBack = () => {
  uni.navigateBack()
}

// 初始化数据
const initData = async () => {
  try {
    // 获取支付方式
    await paymentStore.fetchPaymentMethods()

    // 获取用户余额
    await paymentStore.fetchUserBalance()

    // 获取银行卡列表
    await paymentStore.fetchBankCards()

    // 默认选择第一个可用的支付方式
    if (availablePaymentMethods.value.length > 0) {
      selectPaymentMethod(availablePaymentMethods.value[0])
    }
  } catch (error) {
    console.error('初始化支付页面失败:', error)
  }
}

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.payment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.order-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .order-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .order-no {
      font-size: 24rpx;
      color: #999;
    }
  }

  .order-amount {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .amount-label {
      font-size: 28rpx;
      color: #666;
    }

    .amount-value {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff4757;
    }
  }
}

.payment-methods {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .section-title {
    padding: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  .payment-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 24rpx;
  }
}

.balance-info,
.bank-card-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.discount-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.payment-detail {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    &.total {
      padding-top: 16rpx;
      border-top: 1px solid #f0f0f0;
      font-weight: 600;

      .detail-value {
        font-size: 32rpx;
        color: #ff4757;
      }
    }

    .detail-label {
      font-size: 28rpx;
      color: #666;
    }

    .detail-value {
      font-size: 28rpx;
      color: #333;

      &.discount {
        color: #ff4757;
      }
    }
  }
}

.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 32rpx;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

.bank-card-selector,
.coupon-selector {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    .selector-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .bank-card-list,
  .coupon-list {
    max-height: 60vh;
    overflow-y: auto;
  }

  .bank-card-item,
  .coupon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    &.active {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .card-info {
    .bank-name {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .card-no {
      font-size: 24rpx;
      color: #999;
    }
  }

  .coupon-info {
    flex: 1;

    .coupon-name {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .coupon-desc {
      font-size: 24rpx;
      color: #999;
    }
  }

  .coupon-amount {
    display: flex;
    align-items: center;

    .amount {
      font-size: 28rpx;
      color: #ff4757;
      margin-right: 16rpx;
    }
  }

  .selector-footer {
    padding: 32rpx;
    border-top: 1px solid #f0f0f0;
    text-align: center;
  }
}

.password-input {
  background: white;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  width: 600rpx;

  .password-header {
    text-align: center;
    margin-bottom: 48rpx;

    .password-title {
      display: block;
      font-size: 32rpx;
      color: #333;
      margin-bottom: 16rpx;
    }

    .password-amount {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff4757;
    }
  }

  .password-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 32rpx;
  }
}

.error-text {
  color: #ff4757 !important;
}
</style>

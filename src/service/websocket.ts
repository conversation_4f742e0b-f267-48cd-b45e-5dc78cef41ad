/**
 * websocket.ts
 * WebSocket服务
 *
 * 该文件提供WebSocket连接服务，处理连接建立、消息发送和接收、断线重连等功能
 * 使用uni.connectSocket实现跨平台兼容
 */

import { ref } from 'vue'
import { toast } from '@/utils/toast'

// WebSocket消息类型
export interface WebSocketMessage {
  type: string // 消息类型：chat, notification, heartbeat
  event?: string // 事件类型
  session_id?: number // 会话ID
  sender_id?: number // 发送者ID
  sender_type?: string // 发送者类型
  timestamp: number // 时间戳
  data: any // 消息数据
}

// WebSocket连接状态
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// WebSocket服务类
export class WebSocketService {
  // 单例实例
  private static instance: WebSocketService | null = null

  // WebSocket实例
  private socket: UniApp.SocketTask | null = null
  private url: string
  private token: string = ''
  private tokenProvider: (() => string) | null = null
  private reconnectAttempts: number = 0
  private reconnectMaxTimes: number = 10
  private reconnectInterval: number = 3000
  private heartbeatInterval: number = 30000
  private heartbeatTimer: number | null = null
  private reconnectTimer: number | null = null
  private listeners: Map<string, Function[]> = new Map()

  // 消息队列（存储断线期间的待发送消息）
  private messageQueue: WebSocketMessage[] = []

  // 状态引用，可以在组件中被监听
  public status = ref<WebSocketStatus>(WebSocketStatus.DISCONNECTED)
  public isConnected = ref<boolean>(false)

  private constructor() {
    // 从环境变量获取配置
    this.url = import.meta.env.VITE_WEBSOCKET_URL || ''
    this.reconnectInterval = parseInt(import.meta.env.VITE_WEBSOCKET_RECONNECT_INTERVAL || '3000')
    this.reconnectMaxTimes = parseInt(import.meta.env.VITE_WEBSOCKET_RECONNECT_MAX_TIMES || '10')
    this.heartbeatInterval = parseInt(import.meta.env.VITE_WEBSOCKET_HEARTBEAT_INTERVAL || '30000')
  }

  /**
   * 获取WebSocket服务实例（单例模式）
   */
  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService()
    }
    return WebSocketService.instance
  }

  /**
   * 设置获取最新token的提供函数
   * @param provider 提供最新token的函数
   */
  public setTokenProvider(provider: () => string): void {
    this.tokenProvider = provider
    console.log('已设置WebSocket token提供函数')
  }

  /**
   * 获取最新token（优先使用provider）
   */
  private getLatestToken(): string {
    if (this.tokenProvider) {
      const newToken = this.tokenProvider()
      // 如果token变化了，记录日志
      if (newToken !== this.token) {
        console.log('WebSocket使用了新的token')
      }
      this.token = newToken
      return newToken
    }
    return this.token
  }

  /**
   * 断开WebSocket连接
   */
  public disconnect(): void {
    if (!this.socket) return

    // 重置心跳定时器
    if (this.heartbeatTimer !== null) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    // 重置重连定时器
    if (this.reconnectTimer !== null) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    try {
      this.socket.close({
        success: () => {
          console.log('[WebSocket] 关闭成功')
        },
        fail: (err) => {
          console.error('[WebSocket] 关闭失败', err)
        },
      })
    } catch (error) {
      console.error('[WebSocket] 关闭异常:', error)
    }

    this.socket = null
    this.status.value = WebSocketStatus.DISCONNECTED
    this.isConnected.value = false
    console.log('[WebSocket] 已断开连接')
  }

  /**
   * 发送消息到服务器
   * @param message 消息内容
   * @returns 发送成功返回true，否则返回false
   */
  public sendMessage(message: WebSocketMessage): boolean {
    if (!this.isConnected.value || !this.socket) {
      console.warn('[WebSocket] 未连接，将消息加入队列')
      this.messageQueue.push(message)
      return false
    }

    try {
      this.socket.send({
        data: JSON.stringify(message),
        success: () => {
          console.log('[WebSocket] 消息发送成功')
        },
        fail: (err) => {
          console.error('[WebSocket] 消息发送失败', err)
          // 发送失败，加入队列
          this.messageQueue.push(message)
        },
      })
      return true
    } catch (error) {
      console.error('[WebSocket] 消息发送异常:', error)
      // 发送异常，加入队列
      this.messageQueue.push(message)
      return false
    }
  }

  /**
   * 添加事件监听器
   * @param type 消息类型
   * @param listener 监听函数
   */
  public addEventListener(type: string, listener: Function): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }

    this.listeners.get(type)!.push(listener)
  }

  /**
   * 移除事件监听器
   * @param type 消息类型
   * @param listener 要移除的监听函数
   */
  public removeEventListener(type: string, listener: Function): void {
    if (!this.listeners.has(type)) return

    const listeners = this.listeners.get(type)!
    this.listeners.set(
      type,
      listeners.filter((l) => l !== listener),
    )
  }

  /**
   * 通知指定类型的所有监听器
   * @param type 消息类型
   * @param data 数据
   * @private
   */
  private notifyListeners(type: string, data: any): void {
    if (!this.listeners.has(type)) return

    const listeners = this.listeners.get(type)!
    listeners.forEach((listener) => {
      try {
        listener(data)
      } catch (error) {
        console.error(`[WebSocket] 执行 ${type} 监听器时出错:`, error)
      }
    })
  }

  /**
   * 发送队列中的消息
   * @private
   */
  private flushMessageQueue(): void {
    if (this.messageQueue.length === 0) return

    console.log(`[WebSocket] 发送队列中的消息，数量：${this.messageQueue.length}`)

    // 复制队列并清空原队列
    const queue = [...this.messageQueue]
    this.messageQueue = []

    // 发送队列中的消息
    queue.forEach((message) => {
      this.sendMessage(message)
    })
  }

  /**
   * 启动心跳
   * @private
   */
  private startHeartbeat(): void {
    // 停止已有的心跳
    if (this.heartbeatTimer !== null) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected.value && this.socket) {
        // 发送心跳包
        this.socket.send({
          data: JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }),
          fail: (err) => {
            console.error('[WebSocket] 心跳发送失败', err)
          },
        })
      }
    }, this.heartbeatInterval) as unknown as number
  }

  /**
   * 安排重新连接
   * @private
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer !== null) {
      clearTimeout(this.reconnectTimer)
    }

    if (this.reconnectAttempts >= this.reconnectMaxTimes) {
      console.warn(`[WebSocket] 已达到最大重连尝试次数(${this.reconnectMaxTimes})，停止重连`)
      this.status.value = WebSocketStatus.ERROR
      toast.error('聊天服务连接失败，请重新登录')
      return
    }

    this.reconnectAttempts++

    // 使用递增的重连间隔
    const reconnectTime = this.reconnectInterval * Math.min(this.reconnectAttempts, 3)
    console.log(
      `[WebSocket] ${reconnectTime / 1000}秒后尝试重新连接 (${this.reconnectAttempts}/${this.reconnectMaxTimes})`,
    )

    this.reconnectTimer = setTimeout(() => {
      console.log(
        `[WebSocket] 正在尝试重新连接 (${this.reconnectAttempts}/${this.reconnectMaxTimes})...`,
      )
      // 使用最新的token重连
      const latestToken = this.getLatestToken()
      this.connect(latestToken)
    }, reconnectTime) as unknown as number
  }

  public connect(token: string): Promise<void> {
    // 已经连接或正在连接中，则不重复连接
    if (
      this.status.value === WebSocketStatus.CONNECTED ||
      this.status.value === WebSocketStatus.CONNECTING
    ) {
      return Promise.resolve()
    }

    this.token = token
    this.status.value = WebSocketStatus.CONNECTING

    return new Promise((resolve, reject) => {
      try {
        // 获取最新token
        const latestToken = this.getLatestToken()

        // 构建WebSocket URL（为空时使用默认格式）
        let wsUrl = this.url
        if (!wsUrl) {
          const protocol =
            uni.getSystemInfoSync().platform === 'devtools'
              ? 'ws:'
              : location.protocol === 'https:'
                ? 'wss:'
                : 'ws:'
          const host = import.meta.env.VITE_API_BASE_URL || location.host
          wsUrl = `${protocol}//${host}/api/v1/chat/ws`
        }

        // 添加token参数
        const fullUrl = `${wsUrl}?token=${latestToken}`

        console.log('正在连接WebSocket:', wsUrl)

        // 使用uni.connectSocket连接
        this.socket = uni.connectSocket({
          url: fullUrl,
          success: () => {
            console.log('[WebSocket] 连接创建成功')
          },
          fail: (err) => {
            console.error('[WebSocket] 连接创建失败', err)
            this.status.value = WebSocketStatus.ERROR
            this.isConnected.value = false
            reject(err)
          },
        })

        // 监听WebSocket事件
        this.socket.onOpen(() => {
          console.log('[WebSocket] 连接已打开')
          this.status.value = WebSocketStatus.CONNECTED
          this.isConnected.value = true
          this.reconnectAttempts = 0

          // 发送队列中的消息
          this.flushMessageQueue()

          // 启动心跳
          this.startHeartbeat()

          // 通知连接成功
          this.notifyListeners('connection', { status: 'connected' })

          resolve()
        })

        this.socket.onError((err) => {
          console.error('[WebSocket] 连接错误:', err)
          this.status.value = WebSocketStatus.ERROR
          this.isConnected.value = false

          // 通知错误
          this.notifyListeners('error', { error: err })

          // 尝试重连
          this.scheduleReconnect()

          reject(err)
        })

        this.socket.onClose(() => {
          console.log('[WebSocket] 连接已关闭')
          this.isConnected.value = false

          // 如果不是主动断开连接，则尝试重连
          if (this.status.value !== WebSocketStatus.DISCONNECTED) {
            this.status.value = WebSocketStatus.RECONNECTING
            this.scheduleReconnect()
          }

          // 清除心跳定时器
          if (this.heartbeatTimer !== null) {
            clearInterval(this.heartbeatTimer)
            this.heartbeatTimer = null
          }

          // 通知监听器
          this.notifyListeners('connection', { status: 'disconnected' })
        })

        this.socket.onMessage((res) => {
          try {
            const message = JSON.parse(res.data as string)

            // 特殊处理心跳消息
            if (message.type === 'heartbeat') {
              console.debug('[WebSocket] 收到心跳响应')
              return
            }

            console.log('[WebSocket] 收到消息:', message)

            // 处理认证消息
            if (message.type === 'auth') {
              if (message.status === 'success') {
                console.log('[WebSocket] 认证成功')
              } else {
                console.error('[WebSocket] 认证失败:', message.error)
                this.notifyListeners('auth', { status: 'failed', error: message.error })
                // 关闭连接
                this.disconnect()
                reject(message.error)
                return
              }
            }

            // 通知相应类型的监听器
            this.notifyListeners(message.type, message)

            // 同时也通知全局消息监听器
            this.notifyListeners('message', message)
          } catch (error) {
            console.error('[WebSocket] 解析消息失败:', error, res.data)
          }
        })
      } catch (error) {
        console.error('[WebSocket] 连接异常:', error)
        this.status.value = WebSocketStatus.ERROR
        this.isConnected.value = false
        reject(error)
      }
    })
  }
}

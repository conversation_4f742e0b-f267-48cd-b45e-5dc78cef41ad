import {
  login as _login,
  getUserInfo as _getUserInfo,
  wxLogin as _wxLogin,
  logout as _logout,
  getWxCode,
  refreshToken as _refreshToken,
} from '@/api/login'
import { generateDeviceInfo, showNewDeviceNotification, showRiskWarning } from '@/utils/device'
import { getAccountInfo, getAccountTransactions } from '@/api/account'
import { defineStore } from 'pinia'
import { ref, computed, watchEffect, onMounted, onUnmounted } from 'vue'
import { toast } from '@/utils/toast'
import { IUserInfoVo, ILoginRequest, IDirectRefreshTokenResponse, IWxLoginRequest } from '@/api/login.typings'
import {
  IAccountInfo,
  IAccountTransaction,
  IAccountTransactionsParams,
} from '@/api/account.typings'
import { useWebSocketStore } from './websocket'

// 初始化状态
const userInfoState: IUserInfoVo = {
  id: 0,
  username: '',
  avatar: '/static/images/default-avatar.png',
  token: '',
  refreshToken: '',
  tokenExpiration: 0,
}

// 初始化账户信息状态
const accountInfoState: IAccountInfo = {
  id: 0,
  user_id: 0,
  balance: '0.00',
  frozen_balance: '0.00',
  total_recharge: '0.00',
  total_consume: '0.00',
  status: 1,
  last_recharge: '',
  last_consume: '',
  updated_at: '',
  created_at: '',
}

// 本地存储key
const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  TOKEN: 'token',
  REFRESH_TOKEN: 'refreshToken',
  TOKEN_EXPIRATION: 'tokenExpiration',
  REMEMBER_LOGIN: 'rememberLogin',
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 定义用户信息
    const userInfo = ref<IUserInfoVo>({ ...userInfoState })

    // 定义账户信息
    const accountInfo = ref<IAccountInfo>({ ...accountInfoState })

    // 定义账户变动记录
    const accountTransactions = ref<IAccountTransaction[]>([])

    // 是否记住登录状态
    const rememberMe = ref<boolean>(uni.getStorageSync(STORAGE_KEYS.REMEMBER_LOGIN) || false)

    // 计算token是否接近过期（剩余时间少于3分钟，则为接近过期）
    const isTokenExpiringSoon = computed(() => {
      const expiration = userInfo.value.tokenExpiration || 0
      // 如果未设置过期时间或token不存在，则返回false
      if (!expiration || !userInfo.value.token) return false

      const now = Date.now()
      const expiresInMs = expiration - now
      // 如果已经过期，返回false（避免重复刷新过期的token）
      if (expiresInMs <= 0) return false

      // 如果token有效且剩余时间少于2分钟（120000毫秒），则返回true
      // 根据token有效期和刷新频率进行调整，防止过于频繁的刷新
      return expiresInMs < 120000
    })

    // 获取token到期剩余时间（毫秒）
    const getTokenRemainingTime = (): number => {
      const expiration = userInfo.value.tokenExpiration || 0
      if (!expiration) return 0

      const now = Date.now()
      return Math.max(0, expiration - now)
    }

    // 计算用户是否已登录
    const isLoggedIn = computed(() => {
      return !!userInfo.value.token && (userInfo.value.tokenExpiration || 0) > Date.now()
    })

    // 设置用户信息
    const setUserInfo = (val: IUserInfoVo) => {
      console.log('设置用户信息', val)
      // 若头像为空 则使用默认头像
      if (!val.avatar) {
        val.avatar = userInfoState.avatar
      }
      userInfo.value = val
    }
    const setUserAvatar = (avatar: string) => {
      userInfo.value.avatar = avatar
      console.log('设置用户头像', avatar)
      console.log('userInfo', userInfo.value)
    }
    // 删除用户信息
    const removeUserInfo = () => {
      userInfo.value = { ...userInfoState }
      accountInfo.value = { ...accountInfoState }
      accountTransactions.value = []
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(STORAGE_KEYS.TOKEN)
      uni.removeStorageSync(STORAGE_KEYS.REFRESH_TOKEN)
      uni.removeStorageSync(STORAGE_KEYS.TOKEN_EXPIRATION)
      // 保留记住登录的设置
    }
    /**
     * 用户登录
     * @param params 登录参数
     */
    const login = async (params: ILoginRequest): Promise<boolean> => {
      console.log('login data:', params)
      try {
        // 添加设备信息
        const loginData = {
          ...params,
          device_info: generateDeviceInfo()
        }
        
        const res = await _login(loginData)
        // 直接使用res作为ILoginResponse，因为request函数已经解析了data层
        console.log(res)
        // 设置用户信息
        const now = Date.now()
        const tokenInfo = res.data.token_info
        const userData = res.data.user
        const tokenExpiration = now + tokenInfo.expires_in * 1000
        console.log('登录成功，token过期时间：', new Date(tokenExpiration).toLocaleString())

        // 更新是否记住登录的状态
        rememberMe.value = !!params.rememberLogin
        uni.setStorageSync(STORAGE_KEYS.REMEMBER_LOGIN, rememberMe.value)

        // 将相关信息存在userInfo中
        userInfo.value = {
          ...userData, // 将后端返回的用户信息展开
          token: tokenInfo.access_token,
          refreshToken: tokenInfo.refresh_token,
          tokenExpiration: tokenExpiration,
        }

        // 存储到本地
        uni.setStorageSync(STORAGE_KEYS.TOKEN, tokenInfo.access_token)
        uni.setStorageSync(STORAGE_KEYS.TOKEN_EXPIRATION, tokenExpiration)
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo.value)

        // 根据是否记住登录决定是否保存refreshToken
        if (rememberMe.value) {
          uni.setStorageSync(STORAGE_KEYS.REFRESH_TOKEN, tokenInfo.refresh_token)
        }

        // 保存设备ID
        if (res.data.device_id) {
          uni.setStorageSync('current_device_id', res.data.device_id)
        }

        // 处理新设备登录提示
        if (res.data.is_new_device) {
          showNewDeviceNotification()
        }

        // 处理风险等级警告
        if (res.data.risk_level && res.data.risk_level > 0) {
          showRiskWarning(res.data.risk_level)
        }

        // 登录成功后建立WebSocket连接
        const wsStore = useWebSocketStore()
        wsStore.connectWebSocket()

        toast.success('登录成功')
        return true
      } catch (error) {
        console.error('登录失败', error)
        toast.error('登录失败，请检查用户名和密码')
        return false
      }
    }
    /**
     * 获取用户信息
     */
    const getUserInfo = async () => {
      try {
        const res = await _getUserInfo()
        // 直接使用res作为IUserInfoVo
        console.log('info', res)
        // 确保 res 不为空
        if (!res) {
          console.error('获取用户信息失败：响应数据为空')
          return res
        }

        // 创建一个新对象合并数据，避免直接修改原对象
        const updatedUserData = {
          ...res.data,
          // 保留当前token相关信息，因为getUserInfo API不会返回这些字段
          token: userInfo.value.token,
          refreshToken: userInfo.value.refreshToken,
          tokenExpiration: userInfo.value.tokenExpiration,
        }

        setUserInfo(updatedUserData)
        uni.setStorageSync('userInfo', updatedUserData)
        return res
      } catch (error) {
        console.error('获取用户信息失败', error)
        throw error
      }
    }

    /**
     * 获取账户信息
     */
    const fetchAccountInfo = async () => {
      try {
        const result = await getAccountInfo()
        accountInfo.value = result.data
        console.log('获取账户信息成功:', result.data)
        return result.data
      } catch (error) {
        console.error('获取账户信息失败:', error)
        throw error
      }
    }

    /**
     * 获取账户变动记录
     */
    const fetchAccountTransactions = async (params: IAccountTransactionsParams) => {
      try {
        const result = await getAccountTransactions(params)

        if (params.page === 1) {
          // 第一页，重置数据
          accountTransactions.value = result.data.list
        } else {
          // 追加数据
          accountTransactions.value = [...accountTransactions.value, ...result.data.list]
        }

        console.log('获取账户变动记录成功:', result.data)
        return result.data
      } catch (error) {
        console.error('获取账户变动记录失败:', error)
        throw error
      }
    }

    /**
     * 刷新账户信息（包括账户信息和变动记录）
     */
    const refreshAccountData = async () => {
      if (!isLoggedIn.value) return

      try {
        await Promise.all([
          fetchAccountInfo(),
          fetchAccountTransactions({ page: 1, page_size: 20 }),
        ])
      } catch (error) {
        console.error('刷新账户数据失败', error)
      }
    }
    /**
     * 刷新令牌
     */
    const refreshUserToken = async () => {
      try {
        // 确保userInfo已初始化
        if (!userInfo.value) {
          userInfo.value = { ...userInfoState }
        }

        if (!userInfo.value.refreshToken) {
          throw new Error('刷新令牌不存在')
        }

        const res = await _refreshToken({ refresh_token: userInfo.value.refreshToken })

        // 获取令牌信息 - 适配直接在data中的结构
        const tokenInfo = res.data

        // 更新令牌信息
        const now = Date.now()
        const tokenExpiration = now + tokenInfo.expires_in * 1000
        console.log('刷新令牌成功，新token过期时间：', new Date(tokenExpiration).toLocaleString())

        userInfo.value.token = tokenInfo.access_token
        userInfo.value.refreshToken = tokenInfo.refresh_token
        userInfo.value.tokenExpiration = tokenExpiration

        // 存储新令牌
        uni.setStorageSync(STORAGE_KEYS.TOKEN, tokenInfo.access_token)
        uni.setStorageSync(STORAGE_KEYS.TOKEN_EXPIRATION, tokenExpiration)
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo.value)

        // 只有在记住登录的情况下才保存刷新令牌到本地存储
        if (rememberMe.value) {
          uni.setStorageSync(STORAGE_KEYS.REFRESH_TOKEN, tokenInfo.refresh_token)
        }

        // Token更新后处理WebSocket连接
        const wsStore = useWebSocketStore()

        // 无论当前WebSocket是否连接，都先断开再重新连接
        // 这是因为WebSocket连接可能使用的是旧token
        console.log('刷新token成功，重新连接WebSocket以使用新token')
        wsStore.disconnectWebSocket()
        // 等待短暂停以确保断开完成
        setTimeout(() => {
          wsStore.connectWebSocket()
        }, 500)

        console.log('刷新令牌成功')
        return res
      } catch (error) {
        console.error('刷新令牌失败', error)
        // 刷新失败，清除用户信息，要求重新登录
        removeUserInfo()
        throw error
      }
    }

    /**
     * 退出登录 并 删除用户信息
     */
    const logout = async () => {
      try {
        //await _logout()

        toast.success('退出成功')
      } catch (error) {
        console.error('退出请求失败，但仍会清除本地用户信息', error)
      } finally {
        // 断开WebSocket连接
        const wsStore = useWebSocketStore()
        wsStore.disconnectWebSocket()

        // 清除用户信息
        removeUserInfo()
      }
    }
    /**
     * 微信登录
     */
    const wxLogin = async () => {
      try {
        // 获取微信小程序登录的code
        const codeData = await getWxCode()
        console.log('微信登录code', codeData)

        // 添加设备信息
        const wxLoginData: IWxLoginRequest = {
          code: codeData.code,
          device_info: generateDeviceInfo()
        }

        const res = await _wxLogin(wxLoginData)

        // 获取令牌信息和用户信息
        // 直接使用res作为ILoginResponse
        const tokenInfo = res.data.token_info
        const userData = res.data.user

        // 保存令牌信息
        const now = Date.now()
        const tokenExpiration = now + tokenInfo.expires_in * 1000

        // 更新用户信息
        userInfo.value = {
          ...userData, // 将后端返回的用户信息展开
          token: tokenInfo.access_token,
          refreshToken: tokenInfo.refresh_token,
          tokenExpiration: tokenExpiration,
        }

        // 存储令牌信息到本地
        uni.setStorageSync(STORAGE_KEYS.TOKEN, tokenInfo.access_token)
        uni.setStorageSync(STORAGE_KEYS.TOKEN_EXPIRATION, tokenExpiration)
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo.value) // 添加保存用户信息

        // 根据是否记住登录决定是否保存refreshToken
        if (rememberMe.value) {
          uni.setStorageSync(STORAGE_KEYS.REFRESH_TOKEN, tokenInfo.refresh_token)
        }

        // 保存设备ID
        if (res.data.device_id) {
          uni.setStorageSync('current_device_id', res.data.device_id)
        }

        // 处理新设备登录提示
        if (res.data.is_new_device) {
          showNewDeviceNotification()
        }

        // 处理风险等级警告
        if (res.data.risk_level && res.data.risk_level > 0) {
          showRiskWarning(res.data.risk_level)
        }

        // 登录成功后建立WebSocket连接
        const wsStore = useWebSocketStore()
        wsStore.connectWebSocket()

        toast.success('登录成功')
        return true
      } catch (error) {
        console.error('微信登录失败', error)
        toast.error('微信登录失败，请重试')
        return false
      }
    }

    // 定时器引用，用于清理
    let tokenCheckTimer: number | null = null

    // 开始定时检查token过期状态
    const startTokenExpirationTimer = () => {
      // 先清除可能存在的定时器
      stopTokenExpirationTimer()

      // 如果没有token或refreshToken，不启动定时器
      if (!userInfo.value.token || !userInfo.value.refreshToken) {
        return
      }

      console.log('启动token过期检查定时器')
      // 创建新的定时器，每30秒检查一次token状态
      tokenCheckTimer = setInterval(async () => {
        const remainingTime = getTokenRemainingTime()
        console.log(
          '检查token剩余时间:',
          remainingTime,
          '毫秒，过期时间:',
          new Date(userInfo.value.tokenExpiration || 0).toLocaleString(),
        )
        // 如果token有效且剩余时间少于2分钟，则刷新token
        if (remainingTime > 0 && remainingTime < 120000 && userInfo.value.refreshToken) {
          console.log(
            `Token剩余有效期${Math.floor(remainingTime / 1000)}秒(${(remainingTime / 1000 / 60).toFixed(1)}分钟)，即将过期，自动刷新...`,
          )
          try {
            await refreshUserToken()
            console.log('定时刷新token成功')
          } catch (error) {
            console.error('定时刷新token失败:', error)
            // 刷新失败时停止定时器（refreshUserToken内部会清除用户信息）
            stopTokenExpirationTimer()
          }
        }
      }, 30000) // 每30秒检查一次，提高响应速度
    }

    // 停止定时检查
    const stopTokenExpirationTimer = () => {
      if (tokenCheckTimer !== null) {
        clearInterval(tokenCheckTimer)
        tokenCheckTimer = null
        console.log('停止token过期检查定时器')
      }
    }

    // 自动刷新token机制 - 响应式监听（作为辅助机制）
    watchEffect(async () => {
      if (isTokenExpiringSoon.value && userInfo.value.refreshToken) {
        console.log('watchEffect检测到Token即将过期，自动刷新...')
        try {
          await refreshUserToken()
        } catch (error) {
          console.error('自动刷新token失败:', error)
        }
      }
    })

    // 应用启动时自动登录（如果有refreshToken且已选择记住登录）
    const autoLogin = async () => {
      // 1. 先确保读取本地存储中的记住登录状态
      const savedRememberLogin = uni.getStorageSync(STORAGE_KEYS.REMEMBER_LOGIN)
      rememberMe.value = !!savedRememberLogin
      console.log('记住登录状态:', rememberMe.value ? '是' : '否')

      // 2. 只有在记住登录的情况下才尝试自动登录
      if (!rememberMe.value) {
        console.log('未选择记住登录，不执行自动登录')
        return false
      }

      // 3. 检查是否存在刷新令牌
      const savedRefreshToken = uni.getStorageSync(STORAGE_KEYS.REFRESH_TOKEN)

      if (!savedRefreshToken) {
        console.log('无法找到刷新令牌，自动登录失败')
        return false
      }

      console.log('检测到保存的刷新令牌，尝试自动登录...')
      try {
        // 确保userInfo已初始化
        if (!userInfo.value) {
          userInfo.value = { ...userInfoState }
        }
        userInfo.value.refreshToken = savedRefreshToken
        await refreshUserToken()
        console.log('自动登录成功')
        // 自动登录成功后启动token检查定时器
        startTokenExpirationTimer()
        return true
      } catch (error) {
        console.error('自动登录失败:', error)
        // 清除已失效的刷新令牌
        uni.removeStorageSync(STORAGE_KEYS.REFRESH_TOKEN)
        return false
      }
    }

    // 监听用户登录状态变化，管理定时器
    watchEffect(() => {
      console.log('检测登录状态变化:', isLoggedIn.value ? '已登录' : '未登录')
      if (isLoggedIn.value && userInfo.value.refreshToken) {
        // 用户已登录且有refreshToken，启动定时器
        startTokenExpirationTimer()
      } else {
        // 用户未登录或无refreshToken，停止定时器
        stopTokenExpirationTimer()
      }
    })

    // 执行自动登录
    autoLogin()

    // 组件卸载时清除定时器
    onUnmounted(() => {
      stopTokenExpirationTimer()
    })

    return {
      userInfo,
      accountInfo,
      accountTransactions,
      isLoggedIn,
      login,
      wxLogin,
      getUserInfo,
      setUserAvatar,
      logout,
      refreshUserToken,
      autoLogin,
      rememberMe, // 导出记住登录状态
      startTokenExpirationTimer,
      stopTokenExpirationTimer,
      fetchAccountInfo,
      fetchAccountTransactions,
      refreshAccountData,
    }
  },
  {
    persist: [
      {
        key: 'user-store', // 指定存储键名
        storage: {
          getItem: (key) => {
            const value = uni.getStorageSync(key)
            return value ? JSON.parse(value) : null
          },
          setItem: (key, value) => {
            uni.setStorageSync(key, JSON.stringify(value))
          },
        },
        paths: ['userInfo', 'rememberMe'], // 指定要持久化的状态路径
      },
    ],
  },
)

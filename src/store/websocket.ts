/**
 * websocket.ts
 * WebSocket状态管理Store
 *
 * 该文件提供对WebSocket连接状态的集中管理，处理连接、消息分发等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { WebSocketStatus, WebSocketMessage, WebSocketService } from '@/service/websocket'
import { useUserStore } from './user'
import { useChatStore } from './chat'
import { toast } from '@/utils/toast'

// 创建WebSocket服务实例
const websocketService = WebSocketService.getInstance()

// WebSocket事件回调函数类型
type WebSocketEventCallback = (data: any) => void

export const useWebSocketStore = defineStore(
  'websocket',
  () => {
    // WebSocket连接状态
    const status = ref<WebSocketStatus>(WebSocketStatus.DISCONNECTED)
    const isConnected = ref(false)
    const lastConnectTime = ref(0)
    const messageCallbacks = ref<Map<string, Set<WebSocketEventCallback>>>(new Map())

    // 计算属性
    const isConnectedOrConnecting = computed(() => {
      return (
        status.value === WebSocketStatus.CONNECTED || status.value === WebSocketStatus.CONNECTING
      )
    })

    /**
     * 初始化WebSocket连接
     * 如果有有效的用户Token则自动连接
     */
    const initWebSocket = () => {
      const userStore = useUserStore()

      // 设置token提供函数，确保WebSocket使用最新的token
      websocketService.setTokenProvider(() => {
        return userStore.userInfo.token || ''
      })

      if (userStore.isLoggedIn && userStore.userInfo.token) {
        connectWebSocket()
      }
    }

    /**
     * 连接WebSocket服务
     * 使用当前用户Token进行认证连接
     */
    const connectWebSocket = () => {
      const userStore = useUserStore()

      if (!userStore.isLoggedIn || !userStore.userInfo.token) {
        console.warn('用户未登录，无法连接WebSocket')
        return
      }

      // 确保已设置token提供函数
      websocketService.setTokenProvider(() => {
        return userStore.userInfo.token || ''
      })

      // 同步状态引用
      status.value = websocketService.status.value
      isConnected.value = websocketService.isConnected.value

      // 监听WebSocket服务状态变化
      websocketService.addEventListener('connection', (data: any) => {
        status.value = websocketService.status.value
        isConnected.value = websocketService.isConnected.value

        if (data.status === 'connected') {
          lastConnectTime.value = Date.now()
          console.log('WebSocket连接成功')
        }
      })

      // 监听消息
      websocketService.addEventListener('message', handleMessage)

      // 监听聊天消息
      websocketService.addEventListener('chat', handleChatMessage)

      // 监听通知消息
      websocketService.addEventListener('notification', handleNotificationMessage)

      // 连接WebSocket
      websocketService.connect(userStore.userInfo.token)
      console.log('正在连接WebSocket服务...')
    }

    /**
     * 断开WebSocket连接
     */
    const disconnectWebSocket = () => {
      websocketService.disconnect()
      status.value = WebSocketStatus.DISCONNECTED
      isConnected.value = false
    }

    /**
     * 发送消息
     * @param message WebSocket消息
     * @returns 是否发送成功
     */
    const sendMessage = (message: WebSocketMessage): boolean => {
      if (!isConnected.value) {
        console.warn('WebSocket未连接，尝试重新连接')
        connectWebSocket()
        return false
      }

      return websocketService.sendMessage(message)
    }

    /**
     * 注册消息回调
     * @param type 消息类型
     * @param callback 回调函数
     */
    const registerCallback = (type: string, callback: WebSocketEventCallback) => {
      if (!messageCallbacks.value.has(type)) {
        messageCallbacks.value.set(type, new Set())
      }

      messageCallbacks.value.get(type)!.add(callback)
    }

    /**
     * 注销消息回调
     * @param type 消息类型
     * @param callback 回调函数
     */
    const unregisterCallback = (type: string, callback: WebSocketEventCallback) => {
      if (messageCallbacks.value.has(type)) {
        messageCallbacks.value.get(type)!.delete(callback)
      }
    }

    /**
     * 处理接收到的消息
     * @param message 消息数据
     */
    const handleMessage = (message: WebSocketMessage) => {
      // 分发消息给注册的回调
      if (messageCallbacks.value.has(message.type)) {
        messageCallbacks.value.get(message.type)!.forEach((callback) => {
          try {
            callback(message)
          } catch (error) {
            console.error('执行消息回调时出错:', error)
          }
        })
      }

      // 分发所有消息给通用回调
      if (messageCallbacks.value.has('all')) {
        messageCallbacks.value.get('all')!.forEach((callback) => {
          try {
            callback(message)
          } catch (error) {
            console.error('执行通用消息回调时出错:', error)
          }
        })
      }
    }

    /**
     * 处理聊天消息
     * @param message 消息数据
     */
    const handleChatMessage = (message: WebSocketMessage) => {
      console.log('收到聊天消息:', message)

      // 导入聊天store
      const chatStore = useChatStore()

      // 根据消息事件类型处理
      if (message.event === 'new_message' && message.data) {
        // 新消息事件，调用聊天store的receiveMessage方法
        chatStore.receiveMessage(message.data)
        console.log('✅ [WebSocket] 已将新消息传递给聊天store:', message.data)
      } else if (message.event === 'message_status_update' && message.data) {
        // 消息状态更新事件
        console.log('📝 [WebSocket] 消息状态更新:', message.data)
      } else if (message.event === 'typing_status' && message.data) {
        // 输入状态事件
        console.log('⌨️ [WebSocket] 输入状态更新:', message.data)
      }
    }

    /**
     * 处理通知消息
     * @param message 消息数据
     */
    const handleNotificationMessage = (message: WebSocketMessage) => {
      console.log('收到通知消息:', message)

      // 根据通知类型执行不同操作
      if (message.event === 'new_message') {
        // 新消息通知
        toast.info('您有新消息')
      } else if (message.event === 'friend_request') {
        // 好友请求通知
        toast.info('您收到了一个好友请求')
      }
    }

    return {
      // 状态
      status,
      isConnected,
      isConnectedOrConnecting,
      lastConnectTime,

      // 方法
      initWebSocket,
      connectWebSocket,
      disconnectWebSocket,
      sendMessage,
      registerCallback,
      unregisterCallback,
    }
  },
  {
    persist: false, // WebSocket状态不需要持久化
  },
)
